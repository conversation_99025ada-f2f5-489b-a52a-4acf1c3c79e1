#!/usr/bin/env python3

import csv
import os
import sys
import logging
import argparse
import tempfile
import shutil
from pathlib import Path
from typing import Optional, List, Dict, Tuple
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Standalone implementation of the chargeback transformer
# This version doesn't depend on external config providers

class MetricKeys(Enum):
    DISPUTED_AMOUNT = 'disputed_amount'
    AMOUNT_DEBITED = 'amount_debited'
    AMOUNT_RECOVERED = 'amount_recovered'
    LOSS = 'loss'
    WRONGLY_MARKED_CHARGEBACK_AMOUNT = 'wrongly_marked_chargeback_amount'
    RGCS_ACCEPTED_AND_NPCI_ACCEPTED = 'rgcs_accepted_and_npci_accepted'
    DEBIT_SIGNAL_PROCESSED_PENDING_ON_OPS = 'debit_signal_processed_pending_on_ops'
    PENDING_ON_OPS_TO_MOVE = 'pending_on_ops_to_move'
    ABSORBED_OR_REQUESTED_BY_OPS = 'absorbed_or_requested_by_ops'
    DEBIT_SIGNAL_NOT_PROCESSED = 'debit_signal_not_processed'


class ChannelKeys(Enum):
    UPI_CHARGEBACK = 'upi_chargeback'
    PG_CHARGEBACK = 'pg_chargeback'
    EDC_CHARGEBACK = 'edc_chargeback'
    TOTAL = 'total'


@dataclass
class ChargebackMetrics:
    upi_chargeback: float = 0
    pg_chargeback: float = 0
    edc_chargeback: float = 0
    total: float = 0

    def to_string_dict(self) -> Dict[str, str]:
        return {
            ChannelKeys.UPI_CHARGEBACK.value: str(self.upi_chargeback),
            ChannelKeys.PG_CHARGEBACK.value: str(self.pg_chargeback),
            ChannelKeys.EDC_CHARGEBACK.value: str(self.edc_chargeback),
            ChannelKeys.TOTAL.value: str(self.total)
        }


@dataclass
class DisputeRecord:
    dispute_workflow_id: str
    transaction_id: str
    current_state: int
    dispute_type: int
    dispute_stage: int
    disputed_amount: float
    accepted_amount: float
    raised_at: str
    signal_type: str = None
    signal_amount: float = 0


@dataclass
class ReportHeaders:
    MAIN_REPORT = ["Data (All figures in Rs)", "UPI", "PG", "EDC", "Total"]
    BREAKDOWN_REPORT = [
        "Reasons for 'Merchant recovery yet to be initiated'",
        "UPI", "PG", "EDC", "Total",
        "Description", "Owner", "Expected Actions"
    ]


def parse_raw_dispute_data(filename) -> List[DisputeRecord]:
    """Parse raw dispute data from CSV file into DisputeRecord objects"""
    try:
        disputes = []
        with open(filename, 'r', newline='', encoding='utf-8') as csvfile:
            # Detect delimiter
            first_line = csvfile.readline()
            csvfile.seek(0)
            delimiter = '\t' if '\t' in first_line else ','
            
            reader = csv.reader(csvfile, delimiter=delimiter)
            headers = next(reader)  # Skip header row
            
            for row_num, row in enumerate(reader, 2):
                if len(row) < 8:
                    logging.warning(f"Skipping row {row_num} with insufficient columns: {len(row)}")
                    continue
                
                try:
                    dispute = DisputeRecord(
                        dispute_workflow_id=row[0].strip(),
                        transaction_id=row[1].strip(),
                        current_state=int(row[2]),
                        dispute_type=int(row[3]),
                        dispute_stage=int(row[4]),
                        disputed_amount=float(row[5]) if row[5].isnumeric() else 0.0,
                        accepted_amount=float(row[6]) if row[6].isnumeric() else 0.0,
                        raised_at=row[7].strip(),
                        signal_type=row[8].strip() if row[8] in ['CREDIT', 'DEBIT'] else None,
                        signal_amount=float(row[9]) if row[9].isnumeric() else 0.0
                    )
                    disputes.append(dispute)
                except (ValueError, IndexError) as e:
                    logging.warning(f"Error parsing row {row_num}: {e}")
                    continue
        
        logging.info(f"Parsed {len(disputes)} dispute records from {filename}")
        return disputes
    
    except FileNotFoundError:
        error_msg = f"CSV file not found: {filename}"
        logging.error(error_msg)
        raise FileNotFoundError(error_msg)
    except Exception as e:
        error_msg = f"Failed to parse raw dispute data from {filename}: {e}"
        logging.error(error_msg)
        raise RuntimeError(error_msg)


def get_dispute_type_string(dispute_type: int) -> str:
    """Convert dispute type integer to string"""
    if dispute_type == 0:
        return 'UPI_CHARGEBACK'
    elif dispute_type == 1:
        return 'PG_CHARGEBACK'
    elif dispute_type == 5:
        return 'EDC_CHARGEBACK'
    else:
        return f'UNKNOWN_{dispute_type}'


def _add_to_metrics(metrics: ChargebackMetrics, dispute_type: str, amount: float):
    """Add amount to appropriate metric based on dispute type"""
    if dispute_type == 'UPI_CHARGEBACK':
        metrics.upi_chargeback += amount
    elif dispute_type == 'PG_CHARGEBACK':
        metrics.pg_chargeback += amount
    elif dispute_type == 'EDC_CHARGEBACK':
        metrics.edc_chargeback += amount
    metrics.total += amount


def _calculate_amount_debited(dispute: DisputeRecord) -> float:
    """Calculate amount debited based on dispute conditions"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        (dispute.signal_type != 'CREDIT' or dispute.signal_type is None)):
        return dispute.disputed_amount
    elif (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and 
          dispute.signal_type == 'DEBIT'):
        return dispute.signal_amount
    elif (dispute.dispute_type != 0 and dispute.signal_type == 'DEBIT'):
        return dispute.signal_amount
    else:
        return 0


def _calculate_recovery_initiated(dispute: DisputeRecord) -> float:
    """Calculate recovery initiated based on dispute conditions"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        dispute.signal_type is None and dispute.current_state == 23):
        return dispute.accepted_amount
    elif (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and 
          dispute.signal_type == 'DEBIT' and dispute.current_state == 23):
        return dispute.accepted_amount
    elif (dispute.dispute_type in (1, 5) and dispute.signal_type == 'DEBIT' and 
          dispute.current_state == 23):
        return dispute.accepted_amount
    else:
        return 0


def _calculate_wrongly_marked_chargeback(dispute: DisputeRecord) -> float:
    """Calculate wrongly marked chargeback amount"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        dispute.current_state in [3, 4, 9] and dispute.signal_type is None):
        return dispute.disputed_amount
    elif (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and
          dispute.current_state in [3, 4] and dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    elif (dispute.dispute_type == 1 and dispute.dispute_stage in (0, 1) and
          dispute.current_state in  [3, 30] and dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    elif (dispute.dispute_type == 5 and dispute.dispute_stage in (0, 1) and
          dispute.current_state in  [3, 48] and dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0


def _calculate_absorbed_or_requested_by_ops(dispute: DisputeRecord) -> float:
    """Calculate absorbed or requested by ops amount"""
    if dispute.current_state in (14, 16, 17):
        return dispute.disputed_amount
    else:
        return 0


def _calculate_rgcs_accepted_and_npci_accepted(dispute: DisputeRecord) -> float:
    """Calculate RGCS accepted and NPCI accepted amount"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        dispute.current_state == 2 and dispute.signal_type is None):
        return dispute.disputed_amount
    else:
        return 0


def _calculate_debit_signal_processed_pending_on_ops(dispute: DisputeRecord) -> float:
    """Calculate debit signal processed pending on ops amount"""
    if (dispute.dispute_type in (1, 5) and dispute.current_state == 28 and 
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0


def _calculate_debit_signal_not_processed(dispute: DisputeRecord) -> float:
    """Calculate debit signal not processed amount"""
    if (dispute.dispute_type == 1 and dispute.current_state == 31 and 
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0


def _calculate_pending_on_ops_to_move(dispute: DisputeRecord) -> float:
    """Calculate pending on ops to move amount"""
    if (dispute.dispute_type == 5 and dispute.current_state in (6, 11) and 
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0


def calculate_metrics_from_disputes(disputes: List[DisputeRecord]) -> Dict[str, ChargebackMetrics]:
    """Calculate all metrics from raw dispute data"""
    metrics = {
        MetricKeys.DISPUTED_AMOUNT.value: ChargebackMetrics(),
        MetricKeys.AMOUNT_DEBITED.value: ChargebackMetrics(),
        MetricKeys.AMOUNT_RECOVERED.value: ChargebackMetrics(),
        MetricKeys.LOSS.value: ChargebackMetrics(),
        MetricKeys.WRONGLY_MARKED_CHARGEBACK_AMOUNT.value: ChargebackMetrics(),
        MetricKeys.RGCS_ACCEPTED_AND_NPCI_ACCEPTED.value: ChargebackMetrics(),
        MetricKeys.DEBIT_SIGNAL_PROCESSED_PENDING_ON_OPS.value: ChargebackMetrics(),
        MetricKeys.PENDING_ON_OPS_TO_MOVE.value: ChargebackMetrics(),
        MetricKeys.ABSORBED_OR_REQUESTED_BY_OPS.value: ChargebackMetrics(),
        MetricKeys.DEBIT_SIGNAL_NOT_PROCESSED.value: ChargebackMetrics()
    }
    
    for dispute in disputes:
        dispute_type_str = get_dispute_type_string(dispute.dispute_type)
        
        # Calculate disputed amount
        _add_to_metrics(metrics[MetricKeys.DISPUTED_AMOUNT.value], dispute_type_str, dispute.disputed_amount)
        
        # Calculate amount debited
        amount_debited = _calculate_amount_debited(dispute)
        _add_to_metrics(metrics[MetricKeys.AMOUNT_DEBITED.value], dispute_type_str, amount_debited)
        
        # Calculate recovery initiated
        recovery_initiated = _calculate_recovery_initiated(dispute)
        _add_to_metrics(metrics[MetricKeys.AMOUNT_RECOVERED.value], dispute_type_str, recovery_initiated)
        
        # Calculate loss (amount_debited - recovery_initiated)
        loss = amount_debited - recovery_initiated
        _add_to_metrics(metrics[MetricKeys.LOSS.value], dispute_type_str, loss)
        
        # Calculate other specific metrics
        wrongly_marked = _calculate_wrongly_marked_chargeback(dispute)
        _add_to_metrics(metrics[MetricKeys.WRONGLY_MARKED_CHARGEBACK_AMOUNT.value], dispute_type_str, wrongly_marked)
        
        rgcs_npci = _calculate_rgcs_accepted_and_npci_accepted(dispute)
        _add_to_metrics(metrics[MetricKeys.RGCS_ACCEPTED_AND_NPCI_ACCEPTED.value], dispute_type_str, rgcs_npci)
        
        debit_pending = _calculate_debit_signal_processed_pending_on_ops(dispute)
        _add_to_metrics(metrics[MetricKeys.DEBIT_SIGNAL_PROCESSED_PENDING_ON_OPS.value], dispute_type_str, debit_pending)
        
        pending_ops = _calculate_pending_on_ops_to_move(dispute)
        _add_to_metrics(metrics[MetricKeys.PENDING_ON_OPS_TO_MOVE.value], dispute_type_str, pending_ops)
        
        absorbed = _calculate_absorbed_or_requested_by_ops(dispute)
        _add_to_metrics(metrics[MetricKeys.ABSORBED_OR_REQUESTED_BY_OPS.value], dispute_type_str, absorbed)
        
        debit_not_processed = _calculate_debit_signal_not_processed(dispute)
        _add_to_metrics(metrics[MetricKeys.DEBIT_SIGNAL_NOT_PROCESSED.value], dispute_type_str, debit_not_processed)

    return metrics

def convert_paise_to_rs(amount_in_paisa_str):
    amount = int(amount_in_paisa_str)
    return int(amount / 100)

# Utility functions for CSV operations
def append_row_to_csv(filename, row_data, headers) -> None:
    """Append a row to CSV file"""
    try:
        data_row = [row_data.get(header, "") for header in headers]
        with open(filename, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(data_row)
    except Exception as e:
        logging.error(f"Failed to append row to {filename}: {e}")
        raise e


def write_empty_row(file_path, num_columns) -> None:
    """Write an empty row to CSV file"""
    empty_row = [''] * num_columns
    with open(file_path, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(empty_row)


def create_empty_csv(filename) -> None:
    """Create an empty CSV file"""
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        csv.writer(csvfile)


class MainReportRowGenerator:
    @staticmethod
    def _create_empty_row(headers) -> Dict[str, str]:
        return {header: "" for header in headers}

    @staticmethod
    def _populate_metrics_data(row, headers, metrics) -> None:
        if metrics and len(headers) >= 5:
            metrics_dict = metrics.to_string_dict()
            row[headers[1]] = metrics_dict[ChannelKeys.UPI_CHARGEBACK.value]
            row[headers[2]] = metrics_dict[ChannelKeys.PG_CHARGEBACK.value]
            row[headers[3]] = metrics_dict[ChannelKeys.EDC_CHARGEBACK.value]
            row[headers[4]] = metrics_dict[ChannelKeys.TOTAL.value]

    @classmethod
    def get_total_chargeback_registered_row(cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Total Chargeback Registered on Stratos"
        metrics = data_dictionary.get(MetricKeys.DISPUTED_AMOUNT.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row

    @classmethod
    def get_amount_debited_row(cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Amount debited by NPCI/PGs/Axis Bank\n(i.e. Chargeback Accepted by PhonePe)"
        metrics = data_dictionary.get(MetricKeys.AMOUNT_DEBITED.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row

    @classmethod
    def get_merchant_recovery_initiated_row(cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Merchant recovery initiated from Stratos for Accepted Chargebacks"
        metrics = data_dictionary.get(MetricKeys.AMOUNT_RECOVERED.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row

    @classmethod
    def get_merchant_recovery_pending_row(cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Merchant recovery yet to be initiated for Accepted Chargebacks*"
        metrics = data_dictionary.get(MetricKeys.LOSS.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row


@dataclass
class LossBreakdownRowConfig:
    title: str
    description: str
    owner: str
    expected_actions: str
    metric_key: str


class LossBreakdownReportRowGenerator:
    LOSS_BREAKDOWN_CONFIGS = [
        LossBreakdownRowConfig(
            title="Incorrect Representment Marking",
            description="Chargebacks were accepted by NPCI/PG/Bank but were wrongly marked as \"Representment Completed\" on Stratos. This led to no recovery being initiated.",
            owner="Chargeback Ops",
            expected_actions="Chargeback Ops Team to review and correct the representment status on Stratos.",
            metric_key=MetricKeys.WRONGLY_MARKED_CHARGEBACK_AMOUNT.value
        ),
        LossBreakdownRowConfig(
            title="Discrepancy Between RGCS & NPCI Status",
            description="RGCS shows chargeback acceptance completed, but NPCI still accepted the chargeback and debited funds. This discrepancy led to unexpected loss.",
            owner="Chargeback Ops",
            expected_actions="Ops Team to investigate and provide clarification for such inconsistencies.",
            metric_key=MetricKeys.RGCS_ACCEPTED_AND_NPCI_ACCEPTED.value
        ),
        LossBreakdownRowConfig(
            title="Recovery Pending Post Debit Signal",
            description="Debit signal has been successfully processed, but recovery flow has not been triggered by Ops on Stratos.",
            owner="Chargeback Ops",
            expected_actions="Ops Team to initiate recovery for these accepted chargebacks.",
            metric_key=MetricKeys.DEBIT_SIGNAL_PROCESSED_PENDING_ON_OPS.value
        ),
        LossBreakdownRowConfig(
            title="Chargeback Stuck in Intermediate State",
            description="Chargebacks are still in an investigation or intermediate state and have not moved to the correct recovery path.",
            owner="Chargeback Ops",
            expected_actions="Ops Team to move these chargebacks to the appropriate state for closure/recovery.",
            metric_key=MetricKeys.PENDING_ON_OPS_TO_MOVE.value
        ),
        LossBreakdownRowConfig(
            title="Finance Absorption Pending",
            description="Ops team has requested finance to absorb the chargeback loss for certain cases. These are currently awaiting Finance team's decision.",
            owner="Finance",
            expected_actions="To evaluate and confirm the cases where chargeback loss is to be absorbed.",
            metric_key=MetricKeys.ABSORBED_OR_REQUESTED_BY_OPS.value
        ),
        LossBreakdownRowConfig(
            title="Debit Signal Not Triggered on Stratos",
            description="Chargeback acceptance was correctly marked on Stratos, but debit signal was not processed due to system-level or process miss.",
            owner="Stratos",
            expected_actions="To ensure debit signal is processed for these cases.",
            metric_key=MetricKeys.DEBIT_SIGNAL_NOT_PROCESSED.value
        )
    ]

    @staticmethod
    def _create_empty_breakdown_row(headers) -> Dict[str, str]:
        return {header: "" for header in headers}

    @staticmethod
    def _populate_breakdown_metrics(row, headers, metrics) -> None:
        if metrics and len(headers) >= 5:
            metrics_dict = metrics.to_string_dict()
            row[headers[1]] = metrics_dict[ChannelKeys.UPI_CHARGEBACK.value]
            row[headers[2]] = metrics_dict[ChannelKeys.PG_CHARGEBACK.value]
            row[headers[3]] = metrics_dict[ChannelKeys.EDC_CHARGEBACK.value]
            row[headers[4]] = metrics_dict[ChannelKeys.TOTAL.value]

    @classmethod
    def generate_breakdown_row(cls, config, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_breakdown_row(headers)
        if len(headers) >= 8:
            row[headers[0]] = config.title
            row[headers[5]] = config.description
            row[headers[6]] = config.owner
            row[headers[7]] = config.expected_actions
            metrics = data_dictionary.get(config.metric_key)
            cls._populate_breakdown_metrics(row, headers, metrics)
        return row

    @classmethod
    def generate_all_breakdown_rows(cls, data_dictionary, headers) -> List[Dict[str, str]]:
        return [
            cls.generate_breakdown_row(config, data_dictionary, headers)
            for config in cls.LOSS_BREAKDOWN_CONFIGS
        ]


def _get_loss_reason(dispute: DisputeRecord) -> str:
    """Determine the reason for loss based on dispute conditions"""
    # Incorrect Representment Marking
    if _calculate_wrongly_marked_chargeback(dispute) > 0:
        return "Incorrect Representment Marking"

    # Discrepancy Between RGCS & NPCI Status
    elif _calculate_rgcs_accepted_and_npci_accepted(dispute) > 0:
        return "Discrepancy Between RGCS & NPCI Status"

    # Recovery Pending Post Debit Signal
    elif _calculate_debit_signal_processed_pending_on_ops(dispute) > 0:
        return "Recovery Pending Post Debit Signal"

    # Chargeback Stuck in Intermediate State
    elif _calculate_pending_on_ops_to_move(dispute) > 0:
        return "Chargeback Stuck in Intermediate State"

    # Finance Absorption Pending
    elif _calculate_absorbed_or_requested_by_ops(dispute) > 0:
        return "Finance Absorption Pending"

    # Debit Signal Not Triggered on Stratos
    elif _calculate_debit_signal_not_processed(dispute) > 0:
        return "Debit Signal Not Triggered on Stratos"

    else:
        return "Other"


def generate_loss_breakdown_with_ids(disputes: List[DisputeRecord]) -> List[Tuple[str, str, str, str]]:
    """Generate list of dispute_workflow_id, transaction_id with loss reasons"""
    loss_breakdown = []

    for dispute in disputes:
        dispute_type_str = get_dispute_type_string(dispute.dispute_type)
        amount_debited = _calculate_amount_debited(dispute)
        recovery_initiated = _calculate_recovery_initiated(dispute)
        loss = amount_debited - recovery_initiated

        if loss > 0:  # Only include disputes with actual loss
            reason = _get_loss_reason(dispute)
            if reason:
                loss_breakdown.append((
                    dispute.dispute_workflow_id,
                    dispute.transaction_id,
                    reason,
                    str(loss)
                ))

    return loss_breakdown


def generate_chargeback_report_from_raw_data(input_csv_file) -> str:
    """Generate chargeback report from raw dispute data"""
    try:
        headers = ReportHeaders()
        main_headers = headers.MAIN_REPORT
        breakdown_headers = headers.BREAKDOWN_REPORT

        # Create temporary output file
        temp_csv = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        temp_csv_path = temp_csv.name
        temp_csv.close()

        logging.info(f"Generating report at: {temp_csv_path}")

        # Parse raw dispute data and calculate metrics
        disputes = parse_raw_dispute_data(input_csv_file)
        data_dictionary = calculate_metrics_from_disputes(disputes)

        main_generator = MainReportRowGenerator()
        breakdown_generator = LossBreakdownReportRowGenerator()

        # Write main report headers first
        main_header_row = {header: header for header in main_headers}
        append_row_to_csv(temp_csv_path, main_header_row, main_headers)

        # Generate main report rows
        main_rows = [
            main_generator.get_total_chargeback_registered_row(data_dictionary, main_headers),
            main_generator.get_amount_debited_row(data_dictionary, main_headers),
            main_generator.get_merchant_recovery_initiated_row(data_dictionary, main_headers),
            main_generator.get_merchant_recovery_pending_row(data_dictionary, main_headers)
        ]

        for row in main_rows:
            append_row_to_csv(temp_csv_path, row, main_headers)

        write_empty_row(temp_csv_path, len(main_headers))
        write_empty_row(temp_csv_path, len(main_headers))

        # Generate breakdown report
        breakdown_header_row = {header: header for header in breakdown_headers}
        append_row_to_csv(temp_csv_path, breakdown_header_row, breakdown_headers)

        breakdown_rows = breakdown_generator.generate_all_breakdown_rows(data_dictionary, breakdown_headers)
        for row in breakdown_rows:
            append_row_to_csv(temp_csv_path, row, breakdown_headers)

        # Add loss breakdown with dispute IDs
        write_empty_row(temp_csv_path, len(breakdown_headers))
        write_empty_row(temp_csv_path, len(breakdown_headers))

        # Add detailed loss breakdown header
        loss_detail_headers = ["Dispute Workflow ID", "Transaction ID", "Loss Reason", "Loss Amount"]
        loss_detail_header_row = {header: header for header in loss_detail_headers}
        append_row_to_csv(temp_csv_path, loss_detail_header_row, loss_detail_headers)

        # Add loss breakdown data
        loss_breakdown = generate_loss_breakdown_with_ids(disputes)
        for workflow_id, transaction_id, reason, amount in loss_breakdown:
            loss_row = {
                "Dispute Workflow ID": workflow_id,
                "Transaction ID": transaction_id,
                "Loss Reason": reason,
                "Loss Amount": amount
            }
            append_row_to_csv(temp_csv_path, loss_row, loss_detail_headers)

        logging.info(f"Report generation completed: {temp_csv_path}")
        return temp_csv_path

    except Exception as e:
        error_msg = f"Failed to generate chargeback report from raw data {input_csv_file}: {e}"
        logging.error(error_msg)
        raise RuntimeError(error_msg)


def process_standalone(input_file_path, output_file_path) -> None:
    """Standalone process function that doesn't depend on external utilities"""
    try:
        logging.info(f"Starting standalone chargeback transformation: {input_file_path} -> {output_file_path}")

        # Generate the report
        temp_report_path = generate_chargeback_report_from_raw_data(input_file_path)

        # Copy the report to the final output location
        shutil.copy2(temp_report_path, output_file_path)

        # Clean up temporary file
        os.unlink(temp_report_path)

        logging.info(f"Standalone chargeback transformation completed successfully: {output_file_path}")

    except Exception as e:
        error_msg = f"Failed to process standalone chargeback transformation: {e}"
        logging.error(error_msg)
        raise ValueError(error_msg)


def validate_input_file(file_path: str) -> bool:
    """Validate that the input file exists and has the correct format"""
    if not os.path.exists(file_path):
        print(f"❌ Error: Input file does not exist: {file_path}")
        return False

    # Check if it's a TSV or CSV file
    if not file_path.lower().endswith(('.tsv', '.csv')):
        print(f"⚠️  Warning: Input file should be a TSV or CSV file: {file_path}")

    # Try to read the first few lines to validate format
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # Determine delimiter
            first_line = f.readline()
            if '\t' in first_line:
                delimiter = '\t'
            else:
                delimiter = ','

            f.seek(0)  # Reset to beginning
            reader = csv.reader(f, delimiter=delimiter)
            headers = next(reader)

            # Check if we have at least the minimum required columns
            if len(headers) < 8:
                print(f"⚠️  Warning: Input file has only {len(headers)} columns, expected at least 8")
                print(f"Headers found: {headers}")
                return True  # Still allow processing, transformer will handle it

            print(f"✓ Input file validation passed")
            file_format = 'TSV' if delimiter == '\t' else 'CSV'
            print(f"  - File format: {file_format}")
            print(f"  - Columns found: {len(headers)}")
            print(f"  - Headers: {headers[:8]}...")  # Show first 8 headers

            return True

    except Exception as e:
        print(f"❌ Error validating input file: {e}")
        return False


def generate_output_filename(input_file_path: str, output_file_path: Optional[str] = None) -> str:
    """Generate output filename if not provided"""
    if output_file_path:
        return output_file_path

    # Generate output filename based on input filename
    input_path = Path(input_file_path)
    output_filename = f"{input_path.stem}_chargeback_report.csv"
    output_path = input_path.parent / output_filename

    return str(output_path)


def display_report_summary(output_file_path: str):
    """Display a summary of the generated report"""
    print("\n" + "="*80)
    print("GENERATED REPORT SUMMARY")
    print("="*80)

    try:
        with open(output_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        print(f"📄 Report file: {output_file_path}")
        print(f"📊 Total lines: {len(lines)}")

        # Find key sections
        sections_found = {
            'Main Report': False,
            'Breakdown Report': False,
            'Loss Details': False
        }

        for i, line in enumerate(lines):
            if 'Total Chargeback Registered on Stratos' in line:
                sections_found['Main Report'] = True
                print(f"✓ Main Report section found at line {i+1}")
            elif "Reasons for 'Merchant recovery yet to be initiated'" in line:
                sections_found['Breakdown Report'] = True
                print(f"✓ Breakdown Report section found at line {i+1}")
            elif 'Dispute Workflow ID' in line and 'Transaction ID' in line:
                sections_found['Loss Details'] = True
                print(f"✓ Loss Details section found at line {i+1}")

        print(f"\n📋 Report Sections:")
        for section, found in sections_found.items():
            status = "✓ Present" if found else "✗ Missing"
            print(f"  {section}: {status}")

        # Show first few lines of actual data
        print(f"\n📝 First 10 lines of report:")
        for i, line in enumerate(lines[:10]):
            print(f"  {i+1:2d}: {line.rstrip()}")

        if len(lines) > 10:
            print(f"  ... ({len(lines) - 10} more lines)")

    except Exception as e:
        print(f"❌ Error reading report summary: {e}")


def test_chargeback_transformer_with_file(input_file_path: str, output_file_path: Optional[str] = None) -> bool:
    """
    Test the chargeback transformer with a specific input file

    Args:
        input_file_path: Path to the input TSV/CSV file containing dispute data
        output_file_path: Optional path for the output CSV file. If not provided,
                         will be generated based on input filename

    Returns:
        bool: True if transformation was successful, False otherwise
    """
    print("CHARGEBACK RAW DATA TRANSFORMER - STANDALONE TEST")
    print("="*60)

    try:
        # Validate input file
        print(f"📂 Input file: {input_file_path}")
        if not validate_input_file(input_file_path):
            return False

        # Generate output filename
        output_file_path = generate_output_filename(input_file_path, output_file_path)
        print(f"📄 Output file: {output_file_path}")

        # Ensure output directory exists
        output_dir = os.path.dirname(output_file_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"✓ Created output directory: {output_dir}")

        # Process the data
        print(f"\n🔄 Processing transformation...")
        print(f"   Input:  {input_file_path}")
        print(f"   Output: {output_file_path}")

        process_standalone(input_file_path, output_file_path)

        print("✓ Transformation completed successfully!")

        # Verify output file was created
        if not os.path.exists(output_file_path):
            print(f"❌ Error: Output file was not created: {output_file_path}")
            return False

        # Display report summary
        display_report_summary(output_file_path)

        print(f"\n{'='*60}")
        print("✅ TEST COMPLETED SUCCESSFULLY")
        print(f"{'='*60}")
        print(f"📄 Generated report: {output_file_path}")

        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
#     """Main function with command line argument parsing"""
#     parser = argparse.ArgumentParser(
#         description="Test chargeback raw data transformer with file input (standalone version)",
#         formatter_class=argparse.RawDescriptionHelpFormatter,
#         epilog="""
# Examples:
#   python test_chargeback_transformer_standalone.py input_data.tsv
#   python test_chargeback_transformer_standalone.py input_data.csv -o output_report.csv
#   python test_chargeback_transformer_standalone.py /path/to/dispute_data.tsv -o /path/to/reports/chargeback_analysis.csv
#
# This standalone version includes all the transformer logic and doesn't depend on external config providers.
#         """
#     )
#
#     parser.add_argument(
#         'input_file',
#         help='Path to the input TSV or CSV file containing dispute data'
#     )
#
#     parser.add_argument(
#         '-o', '--output',
#         help='Path for the output CSV file (optional, will be auto-generated if not provided)'
#     )
#
#     parser.add_argument(
#         '-v', '--verbose',
#         action='store_true',
#         help='Enable verbose logging'
#     )
#
#     args = parser.parse_args()
#
#     if args.verbose:
#         logging.getLogger().setLevel(logging.DEBUG)

    # Run the test
    success = test_chargeback_transformer_with_file("/Users/<USER>/Downloads/data_2025-09-07 01_46_57 PM.csv")

    if success:
        print("\n🎉 Transformation completed successfully!")
        print("You can now analyze the generated report.")
    else:
        print("\n💥 Transformation failed!")
        print("Please check the error messages above and ensure your input file is properly formatted.")

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
